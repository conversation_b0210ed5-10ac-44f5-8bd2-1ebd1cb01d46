import React, { useMemo, useState, useEffect } from 'react';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';
import {
  getVerifyQR,
} from '@/services/user';
import { useSnackbar } from 'notistack';
import Paragraph from '@/components/atoms/Paragraph';
import Loading from '@/components/atoms/Loading';
import Document from '@/components/molecules/Document';
import Spacer from '@/components/layout/Spacer';
import Tooltip from '@/components/atoms/Tooltip';
import Icon from '@/components/atoms/Icon';
import dayjs from 'dayjs';
import DocumentInfo from '@/components/organisms/DocumentInfo';
import { useDataQuery } from '@/hooks/useDataQuery';
import SearchFiles from '@/components/molecules/SearchFiles';
import styles from './TablaDocumentosPropietario.module.css';

export default function TablaDocumentosPropietario() {
  const [state, setState] = useState({
    showInfo: false,
    isLoading: true,
  });
  const [user] = useRecoilState(userState);
  const { enqueueSnackbar } = useSnackbar();

  const {
    data,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    isError,
    search: { docName, handleSearchChange },
  } = useDataQuery({ dataType: 'signed-docs-uni' });

  const getDocInfo = (hashArchivo) => async () => {
    try {
      const defaultShareTo = [{ email: user?.idUsuarioA }];
      setState({
        showInfo: true, showShareModal: false, shareTo: defaultShareTo, isLoading: true,
      });
      const { data: docInfo, status } = await getVerifyQR(hashArchivo);
      if (status === 200) {
        const {
          nombreArchivo,
          fechaRegistroStr: fechaRegistro,
          cantidadConsultas,
          firmas,
          ip,
          idArchivo,
          propietario,
          size,
          b64,
          pdfPages,
        } = docInfo.data[0];

        setState({
          ...state,
          showInfo: true,
          showShareModal: false,
          nombreArchivo,
          fechaRegistro,
          cantidadConsultas,
          firmas,
          ip,
          hashArchivo,
          idArchivo,
          propietario,
          size,
          b64,
          pdfPages,
          isLoading: false,
        });
      } else if (status !== 200) {
        setState({
          ...state, showInfo: false, showShareModal: false, isLoading: false,
        });
        enqueueSnackbar(docInfo.mensaje, {
          variant: 'error',
        });
      }
    } catch (e) {
      console.error(e);
    }
  };

  const signedDocsCards = useMemo(() => {
    if (!data) return [];

    return data?.pages?.map((page) => page?.data?.content?.map(({
      idArchivo, fechaRegistro, nombreArchivo, tipoFirma, hashArchivo, estado, descripcion,
    }) => (
      <Document
        style={{ position: 'relative' }}
        onClick={getDocInfo(hashArchivo)}
        key={idArchivo}
        hasCheck={false}
        idArchivo={idArchivo}
        fechaRegistro={fechaRegistro}
        nombreArchivo={nombreArchivo}
        tipoFirma={tipoFirma}
        moreInfo={{
          hour: dayjs(fechaRegistro).format('hh:mm a'),
          descripcion: (
            <>
              <Spacer.Vertical size="sm" />
              <Paragraph size="xs">
                {descripcion?.length > 40 ? `${descripcion.substring(0, 40)}...` : descripcion}
              </Paragraph>
            </>
          ),
          state: (
            <div style={{ position: 'absolute', bottom: 10 }}>
              <Paragraph size="xs" color="muted">{estado}</Paragraph>
            </div>
          ),
        }}
      />
    )));
  }, [data]);

  useEffect(() => {
    if (isLoading) return;

    if (isError || data?.pages?.some((d) => d?.status !== 200)) {
      enqueueSnackbar('Error al cargar los documentos firmados', {
        variant: 'error',
      });

      // LOGOUT AUTOMÁTICO COMENTADO TEMPORALMENTE
      /*
      return async () => {
        const Axios = (await import('axios')).default;
        await Axios.post('/api/logout');
        await router.replace('/login');
      };
      */
    }
  }, [isError, data, isLoading]);

  return (
    <div>
      <DocumentInfo documentInfo={state} handleClick={() => setState({ showInfo: false })}>
        <Paragraph size="sm">
          Para ver más información del documento firmado del que eres propietario por favor
          haz click sobre un documento.
        </Paragraph>
        {/* Implementación de la busqueda */}
        <div className={styles['search-container']}>
          <div className={styles['search-input-wrapper']}>
            <SearchFiles nombreBusqueda={docName} handleBusqueda={handleSearchChange} />
          </div>
          <div className={styles['actions-container']}>
            <Tooltip title="Actualizar">
              <Icon
                onClick={() => refetch({ refetchPage: () => true })}
                name="refresh"
                size="md"
                border="base"
              />
            </Tooltip>
          </div>
        </div>
        <Spacer.Vertical size="sm" />
        <Loading isShown={isLoading}>
          <div className={styles['grid-card']}>
            {signedDocsCards}
          </div>
          {hasNextPage && (
          <div style={{ textAlign: 'center' }}>
            <Tooltip title="Cargar más">
              <Icon
                name="angleDown"
                isClickable
                onClick={() => fetchNextPage()}
                isDisable={isFetchingNextPage}
                border="base"
              />
            </Tooltip>
          </div>
          )}
        </Loading>
      </DocumentInfo>
    </div>
  );
}
