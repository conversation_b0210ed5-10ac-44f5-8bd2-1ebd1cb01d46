import Axios from 'axios';
import { stringify } from 'query-string';
import fetchApi from '@/utils/fetchApi';

/** {@link import('@/pages/api/login')} */
export const getlogin = async (body) => {
  let response;
  try {
    response = await Axios.post(
      '/api/login',
      body,
    );
  } catch (error) {
    response = error.response;
  }

  return response;
};

export const register = async (body) => {
  let response;
  try {
    response = await fetchApi.post(
      '/registro/usuario',
      body,
    );
  } catch (error) {
    response = error.response;
  }

  return response;
};

export const seeSigners = async (idDoc, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      `/firma/manager/ver-firmante/?idArchivo=${idDoc}`,
      undefined,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (error) {
    response = error.response;
  }

  return response;
};

export const reenviarSolicitudFirma = async (body, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/reenviar-solicitud-firmante',
      body,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

/** {@link import('@/pages/api/documentos-solicitud-firma')} */
export const documentosSolicitudFirma = async (accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/solicitudes-usuario',
      undefined,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const getTokenCode = async (token) => {
  let response;
  try {
    response = await fetchApi.post(
      `/token/registro/?codigo=${token}`,
    );
  } catch (error) {
    response = error.response;
  }

  return response;
};

/** {@link import('@/pages/api/firmar')} */
export const signDocument = async (body, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/subir/b64',
      body,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const uploadDocumentsG64 = async (body, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/subir-multiple/b64',
      body,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const signDocuments = async (body, accessToken) => {
  const baseURL = process.env.NEXT_PUBLIC_API_URL;
  let response;
  try {
    response = await fetch(
      `${baseURL}/firma/manager/fimar-multiple`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify(body),
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const sendNotification = async (body, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/enviar-notificacion',
      body,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const getDocsPendingSign = async (idUsuario, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/firma-pendiente',
      stringify({ idUsuario }),
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const getDocsPendingToSign = async (page, perPage, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/v2/docs-pendiente',
      stringify({ page, per_page: perPage }),
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const verifyDocument = async (body) => {
  let response;
  try {
    response = await fetchApi.post(
      '/validacion/registro/verificar-archivo-firmado-64',
      body,
    );
  } catch (error) {
    response = error.response;
  }

  return response;
};

export const getDocsSigned = async (idUsuario, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/documento-firmado',
      stringify({ idUsuario }),
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

/** {@link import('@/pages/api/consultar-by-hash')} */
export const getVerifyQR = async (hash) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/consutlar-by-hash',
      stringify({ hash }),
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const getDocsSignedPag = async (idUsuario, page, perPage, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/docs-firmado',
      stringify({ idUsuario, page, per_page: perPage }),
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const verifiySignProcess = async (idUsuario, csms, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/validarsms-multiple',
      stringify({ usms: idUsuario, csms }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const downloadFile = async (fileID) => {
  let response;
  try {
    response = await fetchApi.post(
      `/validacion/registro/download/${fileID}`,
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const downloadFileB64 = async (fileID) => {
  let response;
  try {
    response = await fetchApi.post(
      `/validacion/registro/downloadB64/${fileID}`,
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const deletePendingDocuments = async (idDocumento, idUsuario, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/delete',
      stringify({ idDocumento, idUsuario }),
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const getServicioUsuario = async (accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/registro/usuario/servicio',
      undefined,
      {
        headers:{
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
  } catch (error) {
    response = error.response;
  }

  return response;
};

export const solicitarCambioContrasena = async (body) => {
  let response;
  try {
    response = await fetchApi.post(
      '/token/registro/solicitud-cambio',
      body,
    );
  } catch (error) {
    response = error.response;
  }

  return response;
};

export const cambiarContrasena = async (body) => {
  let response;
  try {
    response = await fetchApi.put(
      `/registro/usuario/pwd`,
      body,
    );
  } catch (error) {
    response = error.response;
  }

  return response;
};

export const solicitarMultipleFirma = async (body, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/v2/fimar-documentos-multiple-firmante',
      body,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const verificarTokenMultipleFirma = async (code) => {
  let response;
  try {
    response = await fetchApi.post(
      '/token/registro/verifica-token-firmante',
      code,
      {
        headers: {
          'Content-Type': 'text/plain',
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const aceptarTokenMultipleFirma = async (code) => {
  const baseURL = process.env.NEXT_PUBLIC_API_URL;

  let response;
  try {
    response = await fetch(
      `${baseURL}/token/registro/aceptar-token-firmante`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'plain/text',
        },
        body: code,
      },
    );

    response.data = await response.json();
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const validarFirmanteDocumento = async (code, idUsuario, csms) => {
  let response;
  try {
    response = await fetchApi.post(
      '/validacion/registro/validar-firmante-documento',
      code,
      {
        headers: {
          'Content-Type': 'text/plain',
        },
        params: { usms: idUsuario, csms },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const enviarCodigoPorCorreo = async ({ id, tipo }) => {
  let response;
  try {
    response = await fetchApi.post(
      `/validacion/registro/enviar-otp-email?id=${id}&tipo=${tipo}`,
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const newTokenToValidateAccount = async ({ codigo }) => {
  let response;
  try {
    response = await fetchApi.post(
      `/token/registro/solicitar-token-registro?codigo=${codigo}`,
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const getDocsPropietario = async (page, perPage, accessToken, nombre) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/v2/docs-propietario',
      stringify({ page, per_page: perPage, nombre }),
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

/** {@link import('@/pages/api/documentos-firmados')} */
export const getDocsFirmados = async (page, perPage, accessToken, nombre) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/v2/docs-firmados-usuario',
      stringify({
        page, per_page: perPage, sortBy: 'fecha_registro', sortDirection: 'desc', nombre,
      }),
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const saveProfileSettings = async (idServicio, {
  endpointCBack,
  notificarFirma,
  endpointCBackHabilitado,
}, accessToken) => {
  let response;
  try {
    response = await fetchApi.put(
      '/registro/usuario/servicio',
      {
        endpointCBack,
        notificarFirma,
        endpointCBackHabilitado,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const getUserByDocumentNumberOrEmail = async (docOrEmail) => {
  let response;
  try {
    response = await fetchApi.get(
      `/registro/usuario/buscar-documento-email/${docOrEmail}`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const sendOrisRequest = async (data, accessToken) => {
  let response;

  try {
    response = await fetchApi.post(
      '/firma/manager/v2/solicitar-firma-interesado',
      data,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (e) {
    response = e.response;
  }

  return response;
};

export const getPreview = async (fileId, accessToken, token) => {
  let response;

  try {
    const query = token ? `?token=${token}` : '';
    response = await fetchApi.get(
      `/preview/${fileId}${query}`,
      {
        headers: {
          'Content-Type': 'image/png',
          ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
        },
        responseType: 'arraybuffer',
      },
    );
  } catch (e) {
    response = e.response;
  }

  return response;
};

// Nuevo servicio para validar orden de firma
export const validarOrdenFirma = async (idArchivo, emailFirmante, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      `/firma/manager/v2/validar-orden-firma?idArchivo=${idArchivo}&emailFirmante=${emailFirmante}`,
      undefined,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (e) {
    response = e.response;
  }
  return response;
};

// Actualizar servicio para obtener plantillas reales
export const obtenerPlantillas = async (accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/plantillas/v1/listar',
      undefined,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (e) {
    response = e.response;
  }
  return response;
};

// Nuevo servicio para solicitar firma desde oris con o sin plantillas
export const solicitarFirmaPlantillas = async (data, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/plantillas/v1/solicitar-firma',
      data,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );

    // Asegurar que la respuesta tenga la estructura esperada
    if (!response) {
      return {
        status: 500,
        data: {
          mensaje: 'No se recibió respuesta del servidor',
        },
      };
    }

    return response;
  } catch (e) {
    console.error('Error en solicitarFirmaPlantillas:', e);

    // Asegurar que siempre retornemos un objeto con status
    if (e.response) {
      return e.response;
    }

    // Si no hay respuesta, crear una respuesta de error
    return {
      status: 500,
      data: {
        mensaje: e.message || 'Error de conexión con el servidor',
      },
    };
  }
};

export const eliminarDocumentoOris = async (idArchivo, accessToken) => {
  let response;
  try {
    response = await fetchApi.delete(
      `/firma/manager/v2/eliminar-documento-orden?idArchivo=${idArchivo}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (e) {
    response = e.response;
  }
  return response;
};

export const verificarTerminosCondiciones = async (idUsuario, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      `/firma/manager/terminos-condiciones/verificar?idUsuario=${idUsuario}`,
      undefined, // Sin body
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const consultarEstadoSolicitud = async (idArchivo, accessToken) => {
  let response;
  try {
    response = await fetchApi.post(
      '/firma/manager/plantillas/v1/consultar-estado-solicitud',
      { idArchivoFirma: idArchivo },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      },
    );
  } catch (error) {
    response = error.response;
  }
  return response;
};

export const refreshToken = async () => {
  let response;
  try {
    response = await Axios.post('/api/refresh-token', {}, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    response = error.response;
  }
  return response;
};
